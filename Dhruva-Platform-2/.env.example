# Dhruva Platform Environment Configuration Template
# Copy this file to .env and fill in your actual values
# <PERSON>VER commit the actual .env file to version control

# API Configuration
NEXT_PUBLIC_API_KEY="your-api-key-here"
JWT_SECRET_KEY="your-super-secure-jwt-secret-key-minimum-32-characters"
ENV=dev
ADMIN_USER_PASSWORD="your-argon2-hashed-password"
BACKEND_WORKERS=1

# External Services
USE_AWS_TRITON=False
HEARTBEAT_API_KEY="your-heartbeat-api-key"

# MongoDB App Database
MONGO_APP_DB_USERNAME=your-mongo-username
MONGO_APP_DB_PASSWORD=your-secure-mongo-password
APP_DB_NAME=admin
APP_DB_CONNECTION_STRING=mongodb://${MONGO_APP_DB_USERNAME}:${MONGO_APP_DB_PASSWORD}@dhruva-platform-app-db:27017/admin?authSource=admin

# MongoDB Log Database
MONGO_LOG_DB_USERNAME=your-mongo-log-username
MONGO_LOG_DB_PASSWORD=your-secure-mongo-log-password
LOG_DB_NAME=admin
LOG_DB_CONNECTION_STRING=mongodb://${MONGO_LOG_DB_USERNAME}:${MONGO_LOG_DB_PASSWORD}@dhruva-platform-log-db:27017/admin?authSource=admin

# Mongo Express
ME_CONFIG_MONGODB_ADMINUSERNAME="your-mongo-admin-username"
ME_CONFIG_MONGODB_ADMINPASSWORD="your-mongo-admin-password"
ME_CONFIG_BASICAUTH_USERNAME="your-basic-auth-username"
ME_CONFIG_BASICAUTH_PASSWORD="your-basic-auth-password"

# RabbitMQ
RABBITMQ_DEFAULT_USER=your-rabbitmq-user
RABBITMQ_DEFAULT_PASS=your-secure-rabbitmq-password
RABBITMQ_DEFAULT_VHOST=dhruva_host

# Redis Cache
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your-secure-redis-password

# Celery
CELERY_BROKER_URL="pyamqp://your-rabbitmq-user:your-rabbitmq-password@rabbitmq_server:5672/dhruva_host"

# Frontend Configuration
FRONTEND_PORT=3000
NEXT_PUBLIC_BACKEND_API_URL="http://localhost:8000"
FRONTEND_BASE_URL=http://localhost:3000

# Celery Flower
CELERY_FLOWER_BROKER_API="****************************************************************:15672/api/"
CELERY_FLOWER_ADDRESS="0.0.0.0"
CELERY_FLOWER_PORT="5555"
FLOWER_LOGGING_LEVEL="DEBUG"

# Grafana
GRAFANA_AUTH_TOKEN="your-grafana-auth-token"
NEXT_PUBLIC_GRAFANA_URL="http://localhost:3000"
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=your-secure-grafana-password
GRAFANA_SECURE_COOKIE=false

# Prometheus
PROMETHEUS_URL="http://dhruva-platform-pushgateway:9091"
PROM_AGG_GATEWAY_USERNAME="your-prometheus-username"
PROM_AGG_GATEWAY_PASSWORD="your-prometheus-password"
PAG_AUTHUSERS="user=password"

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8000
API_HOST=0.0.0.0
API_PORT=8000
ENVIRONMENT=development

# TimescaleDB Configuration
TIMESCALE_USER=your-timescale-username
TIMESCALE_PASSWORD=your-secure-timescale-password
TIMESCALE_DATABASE_NAME=dhruva
TIMESCALE_HOST=timescaledb
TIMESCALE_PORT=5432
TIMESCALEDB_DB=postgres
TIMESCALEDB_USER=postgres
TIMESCALEDB_PASSWORD=your-secure-timescaledb-password
TIMESCALEDB_HOST=timescaledb
TIMESCALEDB_PORT=5432

# GitHub Integration (if needed)
# GITHUB_PAT=your-github-personal-access-token

# Other Configuration
VAD_DIR=""
SEED_DB=True
MIGRATION_ACTION="migrate"
MAX_SOCKET_CONNECTIONS_PER_WORKER=100
ADMIN_MONGO_PASSWORD=your-admin-mongo-password

# Email Configuration (if using email verification)
EMAIL_SERVICE_PROVIDER=smtp
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=Your Platform Name
EMAIL_VERIFICATION_URL=${FRONTEND_BASE_URL}/verify-email
EMAIL_VERIFICATION_TOKEN_EXPIRY_HOURS=24
EMAIL_VERIFICATION_MAX_ATTEMPTS=5

# SMTP Configuration
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-16-character-app-password
SMTP_USE_TLS=true

# Rate Limiting
RATE_LIMIT_SIGNUP_PER_IP_PER_HOUR=5
RATE_LIMIT_VERIFY_PER_IP_PER_HOUR=10
RATE_LIMIT_RESEND_PER_EMAIL_PER_HOUR=3
RATE_LIMIT_STATUS_PER_IP_PER_HOUR=20
